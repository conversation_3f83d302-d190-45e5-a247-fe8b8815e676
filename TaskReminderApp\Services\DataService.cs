using System;
using System.Data.SQLite;
using System.IO;

namespace TaskReminderApp.Services
{
    public class DataService
    {
        private const string DbName = "TaskReminder.sqlite";
        private static readonly string DbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, DbName);
        private static readonly string ConnectionString = $"Data Source={DbPath};Version=3;";

        public DataService()
        {
            if (!File.Exists(DbPath))
            {
                SQLiteConnection.CreateFile(DbPath);
                CreateTables();
            }
        }

        private void CreateTables()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string createTasksTable = @"
                    CREATE TABLE Tasks (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Title TEXT NOT NULL,
                        Description TEXT,
                        DueDate DATETIME,
                        Priority INTEGER,
                        Status INTEGER,
                        CategoryId INTEGER,
                        IsRecurring BOOLEAN,
                        RecurrencePattern TEXT,
                        CreatedDate DATETIME,
                        CompletedDate DATETIME,
                        FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                    );";

                string createCategoriesTable = @"
                    CREATE TABLE Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Color TEXT,
                        CreatedDate DATETIME
                    );";

                string createRemindersTable = @"
                    CREATE TABLE Reminders (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        TaskId INTEGER,
                        ReminderTime DATETIME,
                        IsActive BOOLEAN,
                        FOREIGN KEY (TaskId) REFERENCES Tasks(Id)
                    );";

                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = createTasksTable;
                    command.ExecuteNonQuery();
                    command.CommandText = createCategoriesTable;
                    command.ExecuteNonQuery();
                    command.CommandText = createRemindersTable;
                    command.ExecuteNonQuery();
                }
            }
        }

        public void AddTask(Models.Task task)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "INSERT INTO Tasks (Title, Description, DueDate, Priority, Status, CategoryId, IsRecurring, RecurrencePattern, CreatedDate) VALUES (@Title, @Description, @DueDate, @Priority, @Status, @CategoryId, @IsRecurring, @RecurrencePattern, @CreatedDate)";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Title", task.Title);
                    command.Parameters.AddWithValue("@Description", task.Description);
                    command.Parameters.AddWithValue("@DueDate", task.DueDate);
                    command.Parameters.AddWithValue("@Priority", task.Priority);
                    command.Parameters.AddWithValue("@Status", task.Status);
                    command.Parameters.AddWithValue("@CategoryId", task.CategoryId);
                    command.Parameters.AddWithValue("@IsRecurring", task.IsRecurring);
                    command.Parameters.AddWithValue("@RecurrencePattern", task.RecurrencePattern);
                    command.Parameters.AddWithValue("@CreatedDate", task.CreatedDate);
                    command.ExecuteNonQuery();
                }
            }
        }

        public Models.Task GetTask(int id)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Tasks WHERE Id = @Id";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Models.Task
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                Title = Convert.ToString(reader["Title"]),
                                Description = Convert.ToString(reader["Description"]),
                                DueDate = Convert.ToDateTime(reader["DueDate"]),
                                Priority = Convert.ToInt32(reader["Priority"]),
                                Status = Convert.ToInt32(reader["Status"]),
                                CategoryId = Convert.ToInt32(reader["CategoryId"]),
                                IsRecurring = Convert.ToBoolean(reader["IsRecurring"]),
                                RecurrencePattern = Convert.ToString(reader["RecurrencePattern"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                CompletedDate = reader["CompletedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CompletedDate"])
                            };
                        }
                    }
                }
            }
            return null;
        }

        public void UpdateTask(Models.Task task)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "UPDATE Tasks SET Title = @Title, Description = @Description, DueDate = @DueDate, Priority = @Priority, Status = @Status, CategoryId = @CategoryId, IsRecurring = @IsRecurring, RecurrencePattern = @RecurrencePattern, CompletedDate = @CompletedDate WHERE Id = @Id";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Title", task.Title);
                    command.Parameters.AddWithValue("@Description", task.Description);
                    command.Parameters.AddWithValue("@DueDate", task.DueDate);
                    command.Parameters.AddWithValue("@Priority", task.Priority);
                    command.Parameters.AddWithValue("@Status", task.Status);
                    command.Parameters.AddWithValue("@CategoryId", task.CategoryId);
                    command.Parameters.AddWithValue("@IsRecurring", task.IsRecurring);
                    command.Parameters.AddWithValue("@RecurrencePattern", task.RecurrencePattern);
                    command.Parameters.AddWithValue("@CompletedDate", task.CompletedDate);
                    command.Parameters.AddWithValue("@Id", task.Id);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteTask(int id)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "DELETE FROM Tasks WHERE Id = @Id";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    command.ExecuteNonQuery();
                }
            }
        }

        public System.Collections.Generic.List<Models.Task> GetAllTasks()
        {
            var tasks = new System.Collections.Generic.List<Models.Task>();
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Tasks";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tasks.Add(new Models.Task
                            {
                                Id = Convert.ToInt32(reader["Id"]),
                                Title = Convert.ToString(reader["Title"]),
                                Description = Convert.ToString(reader["Description"]),
                                DueDate = Convert.ToDateTime(reader["DueDate"]),
                                Priority = Convert.ToInt32(reader["Priority"]),
                                Status = Convert.ToInt32(reader["Status"]),
                                CategoryId = Convert.ToInt32(reader["CategoryId"]),
                                IsRecurring = Convert.ToBoolean(reader["IsRecurring"]),
                                RecurrencePattern = Convert.ToString(reader["RecurrencePattern"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                CompletedDate = reader["CompletedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CompletedDate"])
                            });
                        }
                    }
                }
            }
            return tasks;
        }
    }
}