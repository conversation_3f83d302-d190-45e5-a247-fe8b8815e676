using System.ComponentModel;
using System.Runtime.CompilerServices;
using TaskReminderApp.Models;

namespace TaskReminderApp.ViewModels
{
    public class TaskEditViewModel : INotifyPropertyChanged
    {
        private Models.Task _task;

        public TaskEditViewModel(Models.Task task)
        {
            _task = task;
        }

        public Models.Task Task
        {
            get { return _task; }
        }

        public string? Title
        {
            get { return _task.Title; }
            set { _task.Title = value; OnPropertyChanged(); }
        }

        public string? Description
        {
            get { return _task.Description; }
            set { _task.Description = value; OnPropertyChanged(); }
        }

        public DateTime DueDate
        {
            get { return _task.DueDate; }
            set { _task.DueDate = value; OnPropertyChanged(); }
        }

        public string DueTime
        {
            get { return _task.DueDate.ToString("HH:mm"); }
            set
            {
                if (DateTime.TryParse(value, out var time))
                {
                    _task.DueDate = _task.DueDate.Date + time.TimeOfDay;
                    OnPropertyChanged();
                }
            }
        }

        public int Priority
        {
            get { return _task.Priority; }
            set { _task.Priority = value; OnPropertyChanged(); }
        }

        public System.Collections.Generic.List<string> PriorityLevels { get; } = new System.Collections.Generic.List<string> { "High", "Medium", "Low", "None" };

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}