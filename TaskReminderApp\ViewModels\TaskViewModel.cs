using System.ComponentModel;
using System.Runtime.CompilerServices;
using TaskReminderApp.Models;

namespace TaskReminderApp.ViewModels
{
    public class TaskViewModel : INotifyPropertyChanged
    {
        private Models.Task _task;

        public TaskViewModel(Models.Task task)
        {
            _task = task;
        }

        public int Id
        {
            get { return _task.Id; }
            set { _task.Id = value; OnPropertyChanged(); }
        }

        public string? Title
        {
            get { return _task.Title; }
            set { _task.Title = value; OnPropertyChanged(); }
        }

        public DateTime DueDate
        {
            get { return _task.DueDate; }
            set { _task.DueDate = value; OnPropertyChanged(); }
        }

        public int Priority
        {
            get { return _task.Priority; }
            set { _task.Priority = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}