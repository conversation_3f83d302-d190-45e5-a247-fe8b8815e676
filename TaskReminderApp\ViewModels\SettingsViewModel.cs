using System.ComponentModel;
using System.Runtime.CompilerServices;
using TaskReminderApp.Models;

namespace TaskReminderApp.ViewModels
{
    public class SettingsViewModel : INotifyPropertyChanged
    {
        private AppSettings _appSettings;

        public SettingsViewModel(AppSettings appSettings)
        {
            _appSettings = appSettings;
        }

        public string? Theme
        {
            get { return _appSettings.Theme; }
            set { _appSettings.Theme = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}