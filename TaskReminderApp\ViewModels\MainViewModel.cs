using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using TaskReminderApp.Models;
using TaskReminderApp.Services;

using System.Windows.Input;
using TaskReminderApp.Views;

namespace TaskReminderApp.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly DataService _dataService;
        public ObservableCollection<TaskViewModel> Tasks { get; set; }
        public ICommand AddTaskCommand { get; }

        public MainViewModel()
        {
            _dataService = new DataService();
            Tasks = new ObservableCollection<TaskViewModel>();
            LoadTasks();
            AddTaskCommand = new RelayCommand(AddTask);
        }

        private void LoadTasks()
        {
            var tasks = _dataService.GetAllTasks();
            foreach (var task in tasks)
            {
                Tasks.Add(new TaskViewModel(task));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void AddTask()
        {
            var newTask = new Models.Task();
            var taskEditViewModel = new TaskEditViewModel(newTask);
            var taskEditWindow = new TaskEditWindow
            {
                DataContext = taskEditViewModel
            };

            if (taskEditWindow.ShowDialog() == true)
            {
                _dataService.AddTask(taskEditViewModel.Task);
                Tasks.Add(new TaskViewModel(taskEditViewModel.Task));
            }
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute;
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute == null || _canExecute();
        }

        public void Execute(object? parameter)
        {
            _execute();
        }
    }
}