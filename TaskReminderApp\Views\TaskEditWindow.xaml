<Window x:Class="TaskReminderApp.Views.TaskEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Edit Task" Height="450" Width="400">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Label Grid.Row="0" Grid.Column="0" Content="Title:"/>
        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"/>

        <Label Grid.Row="1" Grid.Column="0" Content="Description:"/>
        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}" TextWrapping="Wrap" AcceptsReturn="True" Height="100"/>

        <Label Grid.Row="2" Grid.Column="0" Content="Due Date:"/>
        <DatePicker Grid.Row="2" Grid.Column="1" SelectedDate="{Binding DueDate, UpdateSourceTrigger=PropertyChanged}"/>

        <Label Grid.Row="3" Grid.Column="0" Content="Due Time:"/>
        <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding DueTime, UpdateSourceTrigger=PropertyChanged}"/>
        
        <Label Grid.Row="4" Grid.Column="0" Content="Priority:"/>
        <ComboBox Grid.Row="4" Grid.Column="1" ItemsSource="{Binding PriorityLevels}" SelectedItem="{Binding Priority, UpdateSourceTrigger=PropertyChanged}"/>

        <StackPanel Grid.Row="5" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="OK" Width="75" Margin="5" Click="OK_Click"/>
            <Button Content="Cancel" Width="75" Margin="5" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>