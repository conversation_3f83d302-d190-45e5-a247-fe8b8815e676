<Window x:Class="TaskReminderApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TaskReminderApp"
        mc:Ignorable="d"
        Title="Task Reminder" Height="600" Width="900">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="LightGray" Padding="10">
            <TextBlock Text="Task Reminder" FontSize="20" FontWeight="Bold"/>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" Background="Gainsboro" Padding="10">
                <StackPanel>
                    <Button Content="All Tasks" Margin="5"/>
                    <Button Content="Today" Margin="5"/>
                    <Button Content="Upcoming" Margin="5"/>
                    <Button Content="Completed" Margin="5"/>
                    <Button Content="Add Task" Margin="5" Command="{Binding AddTaskCommand}"/>
                </StackPanel>
            </Border>

            <!-- Main Panel -->
            <Grid Grid.Column="1">
                <ListView ItemsSource="{Binding Tasks}">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="Title" DisplayMemberBinding="{Binding Title}" />
                            <GridViewColumn Header="Due Date" DisplayMemberBinding="{Binding DueDate, StringFormat='d'}" />
                            <GridViewColumn Header="Priority" DisplayMemberBinding="{Binding Priority}" />
                        </GridView>
                    </ListView.View>
                </ListView>
            </Grid>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="LightGray" Padding="5">
            <TextBlock Text="Status: Ready" HorizontalAlignment="Left"/>
        </Border>
    </Grid>
</Window>
