Windows Task Reminder Application - Comprehensive Development Prompt

Create a production-ready Windows desktop application for task reminder management with the following specifications:

Core Requirements

Application Type
- Platform: Windows desktop application (compatible with Windows 7, 8, 10, 11)
- Framework: Use WPF (Windows Presentation Foundation) with Cand .NET Framework 4.7.2 or .NET 6+ for maximum compatibility
- Architecture: MVVM (Model-View-ViewModel) pattern for maintainable code

Main Features

1. Task Management
- Add Tasks: Create new tasks with title, description, due date/time, and priority level
- Edit Tasks: Modify existing tasks with validation
- Delete Tasks: Remove tasks with confirmation dialogs
- Task Categories: Optional categorization system (Work, Personal, Shopping, etc.)
- Recurring Tasks: Support for daily, weekly, monthly recurring tasks

2. Priority System
- Priority Levels: High (Red), Medium (Orange), Low (Green), None (Gray)
- Visual Indicators: Color-coded priority indicators throughout the UI
- Priority Sorting: Sort tasks by priority level

3. Date and Time Management
- Due Date/Time: Set specific completion deadlines
- Reminder Notifications: Windows toast notifications before due time
- Overdue Detection: Automatic detection and highlighting of overdue tasks
- Time Zone Support: Handle local time zone correctly

4. Task Status Tracking
- Status Types: Pending, In Progress, Completed, Overdue
- Status Transitions: Automatic status updates based on due dates
- Completion Tracking: Move completed tasks to separate section
- Progress Indicators: Visual progress bars for task completion

5. User Interface Requirements

Main Window Layout
- Header: Application title, current date/time, quick stats
- Sidebar: Navigation menu (All Tasks, Today, Upcoming, Completed, Categories)
- Main Panel: Task list with filters and sorting options
- Footer: Status bar with task counts and sync status

Task List Features
- List View: Expandable/collapsible task items
- Grid View: Tabular format with sortable columns
- Search Bar: Real-time search functionality
- Filters: Filter by priority, status, category, date range
- Sorting: Sort by due date, priority, creation date, alphabetical

Task Entry Form
- Task Title: Required field with validation
- Description: Rich text editor for detailed notes
- Due Date: Date picker with calendar widget
- Due Time: Time picker with AM/PM format
- Priority: Dropdown selection
- Category: Dropdown with custom categories
- Recurring: Checkbox with recurrence options
- Attachments: Optional file attachment support

6. Notification System
- Toast Notifications: Windows 10/11 style notifications
- Reminder Alerts: Configurable reminder times (5 min, 15 min, 1 hour, 1 day before)
- Sound Alerts: Optional sound notifications
- Snooze Functionality: Postpone reminders with custom intervals

7. Data Management

Local Storage
- Database: SQLite for local data storage
- Data Model: Tasks, Categories, Settings, Reminders tables
- Backup System: Automatic local backups with restore functionality
- Export/Import: JSON and CSV export/import capabilities

Data Persistence
- Auto-save: Automatic saving of all changes
- Crash Recovery: Restore unsaved data after unexpected shutdown
- Data Validation: Comprehensive input validation and sanitization

8. Error Handling and Reliability

Exception Management
- Global Exception Handler: Catch and log all unhandled exceptions
- User-Friendly Error Messages: Clear, actionable error messages
- Error Logging: Detailed logging to file with rotation
- Graceful Degradation: Application continues functioning despite non-critical errors

Validation and Security
- Input Validation: Validate all user inputs
- SQL Injection Prevention: Use parameterized queries
- File Path Validation: Secure file operations
- Memory Management: Proper disposal of resources

9. Performance Optimization
- Lazy Loading: Load tasks on-demand for large datasets
- Virtual Scrolling: Efficient rendering of large task lists
- Background Processing: Non-blocking operations for database access
- Memory Optimization: Efficient memory usage and garbage collection

10. User Experience Enhancements

Accessibility
- Keyboard Navigation: Full keyboard support
- Screen Reader Support: ARIA labels and descriptions
- High Contrast Mode: Support for Windows high contrast themes
- Font Scaling: Respect Windows font size settings

Customization
- Themes: Light and dark themes
- Window Layout: Resizable and dockable panels
- User Preferences: Customizable settings (default reminder time, theme, etc.)
- Hotkeys: Configurable keyboard shortcuts

11. Additional Features

Reporting and Analytics
- Task Statistics: Completion rates, overdue tasks, productivity metrics
- Reports: Daily, weekly, monthly task reports
- Charts: Visual representations of task completion trends

Integration
- System Tray: Minimize to system tray with quick access
- Startup Integration: Option to start with Windows
- Calendar Integration: Export tasks to calendar applications

Technical Implementation Guidelines

Code Structure
```
TaskReminderApp/
├── Models/
│   ├── Task.cs
│   ├── Category.cs
│   ├── Reminder.cs
│   └── AppSettings.cs
├── ViewModels/
│   ├── MainViewModel.cs
│   ├── TaskViewModel.cs
│   └── SettingsViewModel.cs
├── Views/
│   ├── MainWindow.xaml
│   ├── TaskEditWindow.xaml
│   └── SettingsWindow.xaml
├── Services/
│   ├── DataService.cs
│   ├── NotificationService.cs
│   └── BackupService.cs
├── Utils/
│   ├── ValidationHelper.cs
│   ├── DateTimeHelper.cs
│   └── FileHelper.cs
└── Resources/
    ├── Styles/
    ├── Icons/
    └── Themes/
```

Database Schema
```sql
CREATE TABLE Tasks (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Title TEXT NOT NULL,
    Description TEXT,
    DueDate DATETIME,
    Priority INTEGER,
    Status INTEGER,
    CategoryId INTEGER,
    IsRecurring BOOLEAN,
    RecurrencePattern TEXT,
    CreatedDate DATETIME,
    CompletedDate DATETIME,
    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
);

CREATE TABLE Categories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,
    Color TEXT,
    CreatedDate DATETIME
);

CREATE TABLE Reminders (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TaskId INTEGER,
    ReminderTime DATETIME,
    IsActive BOOLEAN,
    FOREIGN KEY (TaskId) REFERENCES Tasks(Id)
);
```

Key NuGet Packages
- Microsoft.Toolkit.Win32.UI.Controls: Modern Windows controls
- System.Data.SQLite: SQLite database provider
- Newtonsoft.Json: JSON serialization
- Microsoft.Toolkit.Wpf.UI.Controls: Additional UI controls
- Serilog: Structured logging
- AutoMapper: Object-to-object mapping

Error Handling Pattern
```csharp
try
{
    // Operation code
}
catch (SpecificException ex)
{
    // Handle specific exception
    Logger.Error(ex, "Specific error occurred");
    ShowUserFriendlyMessage("Operation failed. Please try again.");
}
catch (Exception ex)
{
    // Handle general exception
    Logger.Fatal(ex, "Unexpected error occurred");
    ShowUserFriendlyMessage("An unexpected error occurred. The application will continue running.");
}
```

Configuration Requirements
- App.config: Application configuration settings
- User Settings: Per-user preferences storage
- Connection Strings: Database connection configuration
- Logging Configuration: Structured logging setup

Deployment Considerations
- Installer: Create MSI installer using WiX Toolset
- Prerequisites: .NET Framework runtime installer
- Auto-updater: Optional automatic update mechanism
- Uninstaller: Clean uninstallation process

Testing Requirements
- Unit Tests: Test all business logic components
- Integration Tests: Test database operations
- UI Tests: Test user interface interactions
- Performance Tests: Test with large datasets

Documentation Requirements
- User Manual: Complete user guide with screenshots
- Technical Documentation: Code documentation and architecture
- Installation Guide: Step-by-step installation instructions
- Troubleshooting Guide: Common issues and solutions

Development Instructions

1. Start with the basic MVVM structure and core models
2. Implement the SQLite database layer with proper migrations
3. Create the main UI layout with responsive design
4. Add task CRUD operations with validation
5. Implement the notification system with Windows integration
6. Add advanced features like search, filters, and reporting
7. Implement comprehensive error handling throughout
8. Add extensive logging for debugging and monitoring
9. Create comprehensive tests for all components
10. Package the application with a professional installer

Quality Assurance Checklist

Functionality
- [ ] All CRUD operations work correctly
- [ ] Notifications trigger at correct times
- [ ] Data persistence works reliably
- [ ] Search and filtering functions properly
- [ ] Recurring tasks generate correctly

User Experience
- [ ] Interface is intuitive and responsive
- [ ] Error messages are clear and helpful
- [ ] Application performance is acceptable
- [ ] Accessibility features work properly
- [ ] Theme switching works correctly

Reliability
- [ ] Application handles large datasets
- [ ] Crash recovery works properly
- [ ] Data backup and restore functions
- [ ] Memory usage is optimized
- [ ] No memory leaks detected

Security
- [ ] Input validation prevents injection attacks
- [ ] File operations are secure
- [ ] Data is stored securely
- [ ] User privacy is protected
- [ ] Error messages don't expose sensitive data